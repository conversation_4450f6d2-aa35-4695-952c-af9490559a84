#!/usr/bin/env python3
"""
Full OneNote Brain Section Processing

This script processes the OneNote Brain section with complete:
- Entity extraction
- Reference processing  
- Embeddings generation
- Real-time monitoring and error handling
"""

import asyncio
import sys
import os
import time
import logging
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from processors.langchain_onenote_processor import LangChainOneNoteProcessor

# Set up detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OneNoteProcessingMonitor:
    """Monitor OneNote processing with real-time feedback."""
    
    def __init__(self):
        self.start_time = None
        self.pages_processed = 0
        self.entities_extracted = 0
        self.references_extracted = 0
        self.embeddings_generated = 0
        self.errors = []
        
    def start_monitoring(self):
        """Start the monitoring session."""
        self.start_time = time.time()
        print("🔄 Starting OneNote Brain Section Processing")
        print("=" * 60)
        print(f"⏰ Start time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 Current entity count in dashboard: 13,748")
        print()
        
    def log_progress(self, message):
        """Log progress with timestamp."""
        elapsed = time.time() - self.start_time if self.start_time else 0
        print(f"[{elapsed:.1f}s] {message}")
        
    def log_error(self, error_msg):
        """Log an error."""
        self.errors.append(error_msg)
        elapsed = time.time() - self.start_time if self.start_time else 0
        print(f"[{elapsed:.1f}s] ❌ ERROR: {error_msg}")
        
    def update_stats(self, pages=0, entities=0, references=0, embeddings=0):
        """Update processing statistics."""
        self.pages_processed += pages
        self.entities_extracted += entities
        self.references_extracted += references
        self.embeddings_generated += embeddings
        
        elapsed = time.time() - self.start_time if self.start_time else 0
        print(f"[{elapsed:.1f}s] 📊 Stats: Pages={self.pages_processed}, Entities={self.entities_extracted}, Refs={self.references_extracted}, Embeddings={self.embeddings_generated}")
        
    def final_report(self):
        """Generate final processing report."""
        elapsed = time.time() - self.start_time if self.start_time else 0
        
        print("\n" + "=" * 60)
        print("📊 FINAL PROCESSING REPORT")
        print("=" * 60)
        print(f"⏰ Total processing time: {elapsed:.1f} seconds")
        print(f"📄 Pages processed: {self.pages_processed}")
        print(f"🧠 Entities extracted: {self.entities_extracted}")
        print(f"📚 References extracted: {self.references_extracted}")
        print(f"🔍 Embeddings generated: {self.embeddings_generated}")
        print(f"❌ Errors encountered: {len(self.errors)}")
        
        if self.errors:
            print(f"\n❌ Error Details:")
            for i, error in enumerate(self.errors, 1):
                print(f"   {i}. {error}")
        
        print(f"\n📈 Entity Count Change:")
        print(f"   Before: 13,748 entities")
        print(f"   Added: {self.entities_extracted} entities")
        print(f"   Expected After: {13748 + self.entities_extracted} entities")
        print(f"   📋 Check dashboard to verify actual count")

async def process_brain_onenote_with_monitoring():
    """Process OneNote Brain section with comprehensive monitoring."""
    monitor = OneNoteProcessingMonitor()
    monitor.start_monitoring()
    
    try:
        # Initialize processor
        monitor.log_progress("🔧 Initializing OneNote processor...")
        processor = LangChainOneNoteProcessor()
        
        if not processor.is_available():
            monitor.log_error("OneNote processor not available")
            return False
        
        monitor.log_progress("✅ OneNote processor initialized")
        
        # Process the Brain section with full features
        monitor.log_progress("🧠 Starting Brain section processing...")
        monitor.log_progress("📋 Processing with: entities=True, references=True, embeddings=True")
        
        result = await processor.process_onenote_from_cloud(
            notebook_name="Biochemistry",
            section_name="Brain",
            page_title=None,  # Process all pages
            extract_entities=True,
            extract_references=True,
            generate_embeddings=True,
            chunk_size=1200,
            overlap=0
        )
        
        # Check for errors in result
        if not result.get('success', False):
            error_msg = result.get('error', 'Unknown error')
            monitor.log_error(f"Processing failed: {error_msg}")
            return False
        
        # Update statistics
        pages = result.get('pages_processed', 0)
        entities = result.get('total_entities', 0)
        references = result.get('total_references', 0)
        embeddings = result.get('total_embeddings', 0)
        
        monitor.update_stats(pages, entities, references, embeddings)
        monitor.log_progress("✅ Processing completed successfully!")
        
        # Show detailed results
        if result.get('detailed_results'):
            monitor.log_progress("📄 Analyzing individual page results...")
            
            detailed_results = result['detailed_results']
            successful_pages = 0
            failed_pages = 0
            
            for i, page_result in enumerate(detailed_results, 1):
                page_title = page_result.get('onenote_metadata', {}).get('page_title', f'Page {i}')
                success = page_result.get('success', False)
                
                if success:
                    successful_pages += 1
                    page_entities = page_result.get('entities_extracted', 0)
                    page_refs = page_result.get('references_extracted', 0)
                    page_embeddings = page_result.get('embeddings_generated', 0)
                    
                    monitor.log_progress(f"✅ {page_title}: E={page_entities}, R={page_refs}, Em={page_embeddings}")
                    
                    # Special attention to ginger page
                    if 'ginger' in page_title.lower():
                        monitor.log_progress(f"🌶️ GINGER PAGE: Found {page_refs} references (expected ~68)")
                else:
                    failed_pages += 1
                    error = page_result.get('error', 'Unknown error')
                    monitor.log_error(f"Page '{page_title}' failed: {error}")
            
            monitor.log_progress(f"📊 Page Summary: {successful_pages} successful, {failed_pages} failed")
        
        return True
        
    except Exception as e:
        monitor.log_error(f"Unexpected error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        monitor.final_report()

async def check_database_connection():
    """Check if database connections are working."""
    print("🔍 Checking Database Connections")
    print("-" * 35)
    
    try:
        # Check Redis connection
        import redis
        redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        redis_client.ping()
        print("✅ Redis connection: Working")
    except Exception as e:
        print(f"❌ Redis connection: Failed - {e}")
        return False
    
    try:
        # Check FalkorDB connection
        import redis
        falkor_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        falkor_client.execute_command("GRAPH.QUERY", "knowledge_graph", "RETURN 1")
        print("✅ FalkorDB connection: Working")
    except Exception as e:
        print(f"❌ FalkorDB connection: Failed - {e}")
        return False
    
    return True

async def main():
    """Main processing function with error handling."""
    print("🌟" * 60)
    print("🧠 OneNote Brain Section - Full Processing")
    print("🌟" * 60)
    
    # Step 1: Check database connections
    db_ok = await check_database_connection()
    if not db_ok:
        print("\n❌ Database connections failed!")
        print("📋 Please start Redis/FalkorDB before processing")
        print("💡 Run: docker-compose up -d")
        return
    
    print("✅ Database connections verified")
    print()
    
    # Step 2: Process OneNote with monitoring
    success = await process_brain_onenote_with_monitoring()
    
    if success:
        print("\n🎉 SUCCESS! OneNote Brain section processed completely!")
        print("\n📋 Next steps:")
        print("   1. Check the dashboard for updated entity count")
        print("   2. Verify new entities in the knowledge graph")
        print("   3. Test search functionality with new content")
        print("   4. Review extracted references")
    else:
        print("\n❌ Processing failed - check errors above")
        print("\n🔧 Troubleshooting:")
        print("   1. Check database connections")
        print("   2. Verify OneNote authentication")
        print("   3. Check available disk space")
        print("   4. Review error logs")
    
    print("\n🌟" * 60)
    print("🎉 Processing Complete!")
    print("🌟" * 60)

if __name__ == "__main__":
    asyncio.run(main())
