#!/usr/bin/env python3
"""
Test Local OCR with qwen2.5vl:3b

This script tests the local OCR setup with <PERSON><PERSON> and qwen2.5vl:3b
using the ginger page content.
"""

import asyncio
import sys
import os
import tempfile
import logging
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.local_ocr_processor import LocalOCRProcessor
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_pdf(text: str, output_path: str) -> bool:
    """Create a test PDF with the given text."""
    try:
        c = canvas.Canvas(output_path, pagesize=letter)
        width, height = letter
        
        # Set up text formatting
        c.setFont("Helvetica", 10)
        line_height = 12
        margin = 50
        max_width = width - 2 * margin
        
        # Split text into lines that fit the page width
        words = text.split()
        lines = []
        current_line = ""
        
        for word in words:
            test_line = current_line + " " + word if current_line else word
            text_width = c.stringWidth(test_line, "Helvetica", 10)
            
            if text_width <= max_width:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        
        if current_line:
            lines.append(current_line)
        
        # Add lines to PDF
        y_position = height - margin
        
        for line in lines:
            if y_position < margin:
                # Start new page
                c.showPage()
                c.setFont("Helvetica", 10)
                y_position = height - margin
            
            c.drawString(margin, y_position, line)
            y_position -= line_height
        
        c.save()
        
        # Check if file was created successfully
        if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            print(f"✅ Test PDF created: {os.path.getsize(output_path)} bytes")
            return True
        else:
            print("❌ PDF creation failed")
            return False
            
    except Exception as e:
        print(f"❌ Error creating PDF: {e}")
        return False

async def get_ginger_content():
    """Get the ginger page content from OneNote."""
    print("🔄 Getting ginger page content...")
    
    try:
        from utils.onenote_auth_manager import OneNoteAuthManager
        import requests
        from bs4 import BeautifulSoup
        
        auth_manager = OneNoteAuthManager()
        access_token = auth_manager.get_valid_token()
        
        if not access_token:
            print("❌ No valid access token")
            return None
        
        headers = {'Authorization': f'Bearer {access_token}'}
        
        # Get the Brain section
        sections_url = "https://graph.microsoft.com/v1.0/me/onenote/sections?$filter=displayName eq 'Brain'"
        sections_response = requests.get(sections_url, headers=headers)
        
        if sections_response.status_code != 200:
            print(f"❌ Failed to get Brain section: {sections_response.status_code}")
            return None
        
        sections = sections_response.json().get('value', [])
        if not sections:
            print("❌ Brain section not found")
            return None
        
        brain_section_id = sections[0]['id']
        
        # Get pages in the Brain section
        pages_url = f"https://graph.microsoft.com/v1.0/me/onenote/sections/{brain_section_id}/pages"
        pages_response = requests.get(pages_url, headers=headers)
        
        if pages_response.status_code != 200:
            print(f"❌ Failed to get pages: {pages_response.status_code}")
            return None
        
        pages = pages_response.json().get('value', [])
        
        # Find the ginger page
        ginger_page = None
        for page in pages:
            title = page.get('title', '').lower()
            if 'ginger' in title:
                ginger_page = page
                break
        
        if not ginger_page:
            print("❌ Ginger page not found")
            return None
        
        print(f"✅ Found ginger page: {ginger_page['title']}")
        
        # Get the page content
        page_id = ginger_page['id']
        content_url = f"https://graph.microsoft.com/v1.0/me/onenote/pages/{page_id}/content"
        content_response = requests.get(content_url, headers=headers)
        
        if content_response.status_code == 200:
            html_content = content_response.text
            
            # Extract text from HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text
            text = soup.get_text()
            
            # Clean up whitespace
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            print(f"✅ Retrieved and processed content: {len(text)} characters")
            return text
        else:
            print(f"❌ Failed to get page content: {content_response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error getting ginger content: {e}")
        return None

def test_poppler_installation():
    """Test if Poppler is properly installed."""
    print("🔧 Testing Poppler Installation")
    print("-" * 30)
    
    poppler_path = os.path.join(os.getcwd(), "poppler", "poppler-23.01.0", "Library", "bin")
    
    print(f"📁 Poppler path: {poppler_path}")
    print(f"📁 Path exists: {os.path.exists(poppler_path)}")
    
    if os.path.exists(poppler_path):
        # Check for key executables
        key_files = ['pdftoppm.exe', 'pdfinfo.exe', 'pdftotext.exe']
        
        for file in key_files:
            file_path = os.path.join(poppler_path, file)
            exists = os.path.exists(file_path)
            print(f"   📄 {file}: {'✅' if exists else '❌'}")
        
        return True
    else:
        print("❌ Poppler path not found")
        return False

def test_ollama_qwen():
    """Test if Ollama and qwen2.5vl:3b are available."""
    print("\n🤖 Testing Ollama and qwen2.5vl:3b")
    print("-" * 35)
    
    try:
        import requests
        
        # Check Ollama
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama is running")
            
            models = response.json().get('models', [])
            model_names = [model.get('name', '') for model in models]
            
            print(f"📋 Available models: {len(model_names)}")
            for model in model_names:
                print(f"   📄 {model}")
            
            # Check for qwen2.5vl:3b
            if 'qwen2.5vl:3b' in model_names:
                print("✅ qwen2.5vl:3b is available")
                return True
            else:
                print("❌ qwen2.5vl:3b not found")
                print("🔄 Attempting to install qwen2.5vl:3b...")
                
                # Try to install the model
                install_response = requests.post(
                    "http://localhost:11434/api/pull",
                    json={"name": "qwen2.5vl:3b"},
                    timeout=300
                )
                
                if install_response.status_code == 200:
                    print("✅ qwen2.5vl:3b installation started")
                    return True
                else:
                    print("❌ Failed to install qwen2.5vl:3b")
                    return False
        else:
            print("❌ Ollama not running")
            return False
            
    except Exception as e:
        print(f"❌ Error checking Ollama: {e}")
        return False

async def test_local_ocr_comprehensive():
    """Comprehensive test of local OCR with qwen2.5vl:3b."""
    print("\n🔍 Comprehensive Local OCR Test")
    print("=" * 40)
    
    # Initialize local OCR processor
    local_ocr = LocalOCRProcessor()
    
    print(f"📋 Local OCR available: {'✅' if local_ocr.is_available() else '❌'}")
    
    if not local_ocr.is_available():
        print("❌ Local OCR not available - check Ollama and model installation")
        return
    
    # Get ginger content
    ginger_content = await get_ginger_content()
    
    if not ginger_content:
        print("❌ Could not get ginger content, using test content")
        ginger_content = """
        Ginger Neuroprotective Effects Test Content
        
        This is a test document for OCR processing with qwen2.5vl:3b.
        
        References:
        1. Smith, J. et al. (2020). Ginger compounds and neuroprotection. Journal of Neuroscience, 45(3), 123-135. DOI: 10.1016/j.neurosci.2020.01.001
        2. Johnson, A.B., Brown, C.D. (2019). Gingerol effects on brain health. Nature Neuroscience, 32(8), 456-467. PMID: 31234567
        3. Lee, K.H. (2021). Anti-inflammatory properties of ginger. Brain Research, 1678, 234-245. DOI: 10.1016/j.brainres.2021.02.001
        """
    
    # Create test PDF
    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_pdf:
        pdf_path = temp_pdf.name
    
    print(f"\n📄 Creating test PDF...")
    if not create_test_pdf(ginger_content[:2000], pdf_path):  # Use first 2000 chars for testing
        print("❌ Could not create test PDF")
        return
    
    # Test OCR processing
    print(f"\n🔄 Processing PDF with Local OCR (qwen2.5vl:3b)...")
    
    try:
        ocr_result = local_ocr.process_pdf_with_local_ocr(pdf_path, focus_on_references=True)
        
        if ocr_result:
            print(f"✅ Local OCR successful!")
            print(f"📊 Input text length: {len(ginger_content[:2000])} characters")
            print(f"📊 OCR output length: {len(ocr_result)} characters")
            print(f"📊 Accuracy estimate: {min(100, len(ocr_result) / len(ginger_content[:2000]) * 100):.1f}%")
            
            # Show sample output
            print(f"\n📋 Sample OCR Output:")
            print(f"   {ocr_result[:300]}...")
            
            # Count potential references
            import re
            ref_patterns = [r'\d+\.', r'\[\d+\]', r'doi:', r'pmid:', r'et al\.']
            total_refs = 0
            for pattern in ref_patterns:
                matches = re.findall(pattern, ocr_result, re.IGNORECASE)
                total_refs += len(matches)
            
            print(f"\n📚 Reference Detection:")
            print(f"   📊 Potential references found: {total_refs}")
            
            # Compare with original
            original_refs = 0
            for pattern in ref_patterns:
                matches = re.findall(pattern, ginger_content[:2000], re.IGNORECASE)
                original_refs += len(matches)
            
            print(f"   📊 Original references: {original_refs}")
            print(f"   📊 Detection rate: {(total_refs/original_refs*100) if original_refs > 0 else 0:.1f}%")
            
            return True
        else:
            print("❌ Local OCR failed")
            return False
            
    except Exception as e:
        print(f"❌ Local OCR error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up
        try:
            os.unlink(pdf_path)
        except:
            pass

async def main():
    """Main test function."""
    print("🌟" * 60)
    print("🤖 Local OCR with qwen2.5vl:3b Test")
    print("🌟" * 60)
    
    # Step 1: Test Poppler installation
    poppler_ok = test_poppler_installation()
    
    # Step 2: Test Ollama and qwen2.5vl:3b
    ollama_ok = test_ollama_qwen()
    
    # Step 3: Comprehensive OCR test
    if poppler_ok and ollama_ok:
        ocr_success = await test_local_ocr_comprehensive()
        
        print(f"\n🎯 FINAL RESULTS:")
        print("=" * 30)
        print(f"   🔧 Poppler: {'✅' if poppler_ok else '❌'}")
        print(f"   🤖 Ollama/qwen2.5vl:3b: {'✅' if ollama_ok else '❌'}")
        print(f"   🔍 Local OCR: {'✅' if ocr_success else '❌'}")
        
        if poppler_ok and ollama_ok and ocr_success:
            print(f"\n🎉 SUCCESS! Local OCR with qwen2.5vl:3b is working!")
            print(f"📋 You now have:")
            print(f"   ✅ Complete local OCR solution")
            print(f"   ✅ No external API dependencies")
            print(f"   ✅ High-quality vision model for text extraction")
            print(f"   ✅ Reference-focused processing")
        else:
            print(f"\n⚠️ Some components need attention")
            if not poppler_ok:
                print(f"   🔧 Fix Poppler installation")
            if not ollama_ok:
                print(f"   🤖 Install/start Ollama and qwen2.5vl:3b")
            if not ocr_success:
                print(f"   🔍 Debug OCR processing")
    else:
        print(f"\n❌ Prerequisites not met:")
        if not poppler_ok:
            print(f"   🔧 Poppler installation failed")
        if not ollama_ok:
            print(f"   🤖 Ollama/qwen2.5vl:3b not available")
    
    print("\n🌟" * 60)
    print("🎉 Test Complete!")
    print("🌟" * 60)

if __name__ == "__main__":
    asyncio.run(main())
