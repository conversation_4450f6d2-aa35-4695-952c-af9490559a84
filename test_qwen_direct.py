#!/usr/bin/env python3
"""
Test qwen2.5vl:3b directly with a simple image.
"""

import requests
import base64
import tempfile
import os
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter

def create_simple_test_image():
    """Create a simple test image with text."""
    try:
        # Create a simple PDF first
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_pdf:
            pdf_path = temp_pdf.name
        
        c = canvas.Canvas(pdf_path, pagesize=letter)
        c.setFont("Helvetica", 16)
        c.drawString(100, 700, "Test Document")
        c.drawString(100, 650, "This is a test for OCR.")
        c.drawString(100, 600, "Reference: Smith, J. (2020). Test paper.")
        c.drawString(100, 550, "DOI: 10.1000/test.2020.001")
        c.save()
        
        # Convert to image using Poppler
        try:
            import pdf2image
            
            poppler_path = os.path.join(os.getcwd(), "poppler", "poppler-23.01.0", "Library", "bin")
            images = pdf2image.convert_from_path(pdf_path, dpi=150, poppler_path=poppler_path)
            
            if images:
                with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_img:
                    images[0].save(temp_img.name, 'PNG')
                    print(f"✅ Test image created: {temp_img.name}")
                    
                    # Clean up PDF
                    os.unlink(pdf_path)
                    
                    return temp_img.name
        except Exception as e:
            print(f"❌ Error creating image: {e}")
        
        # Clean up
        try:
            os.unlink(pdf_path)
        except:
            pass
        
        return None
        
    except Exception as e:
        print(f"❌ Error in create_simple_test_image: {e}")
        return None

def image_to_base64(image_path):
    """Convert image to base64."""
    try:
        with open(image_path, 'rb') as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
            return encoded_string
    except Exception as e:
        print(f"❌ Error encoding image: {e}")
        return None

def test_qwen_vision_api():
    """Test qwen2.5vl:3b vision API directly."""
    print("🤖 Testing qwen2.5vl:3b Vision API Directly")
    print("=" * 45)
    
    # Create test image
    image_path = create_simple_test_image()
    if not image_path:
        print("❌ Could not create test image")
        return False
    
    try:
        # Convert to base64
        image_base64 = image_to_base64(image_path)
        if not image_base64:
            print("❌ Could not encode image")
            return False
        
        print(f"✅ Image encoded: {len(image_base64)} characters")
        
        # Test different API endpoints and formats
        test_configs = [
            {
                'name': 'Standard Vision API',
                'url': 'http://localhost:11434/api/generate',
                'payload': {
                    "model": "qwen2.5vl:3b",
                    "prompt": "Please extract all text from this image, paying special attention to any references, DOIs, or citations.",
                    "images": [image_base64],
                    "stream": False
                }
            },
            {
                'name': 'Chat API',
                'url': 'http://localhost:11434/api/chat',
                'payload': {
                    "model": "qwen2.5vl:3b",
                    "messages": [
                        {
                            "role": "user",
                            "content": "Please extract all text from this image, paying special attention to any references, DOIs, or citations.",
                            "images": [image_base64]
                        }
                    ],
                    "stream": False
                }
            }
        ]
        
        for config in test_configs:
            print(f"\n🔄 Testing {config['name']}...")
            
            try:
                response = requests.post(
                    config['url'],
                    json=config['payload'],
                    timeout=60
                )
                
                print(f"   📊 Status: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    
                    # Extract response text based on API format
                    if 'response' in result:
                        extracted_text = result['response']
                    elif 'message' in result and 'content' in result['message']:
                        extracted_text = result['message']['content']
                    else:
                        extracted_text = str(result)
                    
                    print(f"   ✅ Success! Extracted {len(extracted_text)} characters")
                    print(f"   📄 Response: {extracted_text[:200]}...")
                    
                    # Check for expected content
                    expected_terms = ['Test Document', 'Smith', 'DOI', '10.1000']
                    found_terms = [term for term in expected_terms if term.lower() in extracted_text.lower()]
                    
                    print(f"   📋 Found {len(found_terms)}/{len(expected_terms)} expected terms: {found_terms}")
                    
                    if len(found_terms) >= 2:
                        print(f"   🎉 OCR working well!")
                        return True
                    else:
                        print(f"   ⚠️ OCR may need improvement")
                
                else:
                    print(f"   ❌ Failed: {response.status_code}")
                    try:
                        error_data = response.json()
                        print(f"   📋 Error: {error_data}")
                    except:
                        print(f"   📋 Raw response: {response.text[:200]}...")
                        
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        return False
        
    finally:
        # Clean up
        try:
            os.unlink(image_path)
        except:
            pass

def test_simple_text_api():
    """Test if qwen2.5vl:3b responds to simple text prompts."""
    print("\n🔤 Testing Simple Text API")
    print("-" * 25)
    
    try:
        payload = {
            "model": "qwen2.5vl:3b",
            "prompt": "What is ginger good for?",
            "stream": False
        }
        
        response = requests.post(
            'http://localhost:11434/api/generate',
            json=payload,
            timeout=30
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            text_response = result.get('response', '')
            print(f"✅ Text API working! Response: {text_response[:100]}...")
            return True
        else:
            print(f"❌ Text API failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function."""
    print("🌟" * 60)
    print("🤖 Direct qwen2.5vl:3b API Test")
    print("🌟" * 60)
    
    # Test simple text first
    text_works = test_simple_text_api()
    
    # Test vision capabilities
    vision_works = test_qwen_vision_api()
    
    print(f"\n🎯 RESULTS:")
    print("=" * 20)
    print(f"   🔤 Text API: {'✅' if text_works else '❌'}")
    print(f"   👁️ Vision API: {'✅' if vision_works else '❌'}")
    
    if text_works and vision_works:
        print(f"\n🎉 SUCCESS! qwen2.5vl:3b is fully working!")
        print(f"📋 The model can:")
        print(f"   ✅ Process text prompts")
        print(f"   ✅ Process images with OCR")
        print(f"   ✅ Extract text from documents")
        print(f"   ✅ Identify references and citations")
    elif text_works:
        print(f"\n⚠️ Text API works, but vision API needs debugging")
        print(f"📋 The model is available but vision features may need different API format")
    else:
        print(f"\n❌ Model not responding to API calls")
        print(f"📋 Even though 'ollama run qwen2.5vl:3b' works, API access is not working")
    
    print("\n🌟" * 60)
    print("🎉 Test Complete!")
    print("🌟" * 60)

if __name__ == "__main__":
    main()
