#!/usr/bin/env python3
"""Check if qwen2.5vl:3b model is available."""

import requests

try:
    response = requests.get('http://localhost:11434/api/tags')
    if response.status_code == 200:
        models = [m['name'] for m in response.json().get('models', [])]
        print('Available models:')
        for m in models:
            print(f'  {m}')
        
        qwen_available = 'qwen2.5vl:3b' in models
        print(f'\nqwen2.5vl:3b available: {qwen_available}')
        
        if not qwen_available:
            print('\nModel still downloading. This can take several minutes.')
            print('The model is quite large (~2GB).')
    else:
        print(f'Error: {response.status_code}')
except Exception as e:
    print(f'Error: {e}')
