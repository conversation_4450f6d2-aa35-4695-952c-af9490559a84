#!/usr/bin/env python3
"""
Automated OneNote Brain Section Full Ingestion Pipeline

This script provides a complete automated ingestion pipeline that:
- Handles authentication automatically
- Processes OneNote content into knowledge graph
- Extracts entities, references, and generates embeddings
- Updates entity count in real-time
- Monitors progress and handles errors
"""

import asyncio
import sys
import os
import time
import logging
import json
import tempfile
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import all necessary components
from utils.onenote_auth_manager import OneNoteAuthManager
from utils.entity_extraction_client import EntityExtractionClient
from utils.redis_vector_search import RedisVectorSearch
from utils.embedding_utils import EmbeddingUtils
from database.falkordb_adapter import FalkorDBAdapter
from database.redis_service import RedisService
from database.reference_adapter import ReferenceAdapter

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AutomatedIngestionPipeline:
    """Complete automated ingestion pipeline for OneNote content."""
    
    def __init__(self):
        self.auth_manager = OneNoteAuthManager()
        self.entity_client = EntityExtractionClient()
        self.vector_search = RedisVectorSearch()
        self.embedding_utils = EmbeddingUtils()
        self.falkor_db = FalkorDBAdapter()
        self.redis_service = RedisService()
        self.reference_adapter = ReferenceAdapter()
        
        self.start_time = None
        self.stats = {
            'pages_processed': 0,
            'entities_added': 0,
            'references_added': 0,
            'embeddings_generated': 0,
            'chunks_processed': 0,
            'errors': []
        }
        
    def log_progress(self, message):
        """Log progress with timestamp."""
        elapsed = time.time() - self.start_time if self.start_time else 0
        print(f"[{elapsed:.1f}s] {message}")
        logger.info(message)
        
    def log_error(self, error_msg):
        """Log an error."""
        self.stats['errors'].append(error_msg)
        elapsed = time.time() - self.start_time if self.start_time else 0
        print(f"[{elapsed:.1f}s] ❌ ERROR: {error_msg}")
        logger.error(error_msg)
        
    async def ensure_authentication(self):
        """Ensure we have valid authentication."""
        self.log_progress("🔐 Checking authentication...")
        
        access_token = self.auth_manager.get_valid_token()
        if not access_token:
            self.log_progress("🔄 No valid token, refreshing authentication...")
            
            # Try to refresh token
            if self.auth_manager.refresh_token():
                self.log_progress("✅ Token refreshed successfully")
                return True
            else:
                self.log_error("Authentication failed - manual re-auth required")
                return False
        else:
            self.log_progress("✅ Valid authentication found")
            return True
    
    async def get_brain_content(self):
        """Get all content from the Brain section."""
        try:
            import requests
            from bs4 import BeautifulSoup
            
            access_token = self.auth_manager.get_valid_token()
            headers = {'Authorization': f'Bearer {access_token}'}
            
            # Get the Brain section
            self.log_progress("🔍 Finding Brain section...")
            sections_url = "https://graph.microsoft.com/v1.0/me/onenote/sections?$filter=displayName eq 'Brain'"
            sections_response = requests.get(sections_url, headers=headers, timeout=30)
            
            if sections_response.status_code == 401:
                self.log_progress("🔄 Token expired, refreshing...")
                if await self.ensure_authentication():
                    access_token = self.auth_manager.get_valid_token()
                    headers = {'Authorization': f'Bearer {access_token}'}
                    sections_response = requests.get(sections_url, headers=headers, timeout=30)
                else:
                    return []
            
            if sections_response.status_code != 200:
                self.log_error(f"Failed to get Brain section: {sections_response.status_code}")
                return []
            
            sections = sections_response.json().get('value', [])
            if not sections:
                self.log_error("Brain section not found")
                return []
            
            brain_section_id = sections[0]['id']
            self.log_progress(f"✅ Found Brain section")
            
            # Get pages in the Brain section
            self.log_progress("📄 Getting pages from Brain section...")
            pages_url = f"https://graph.microsoft.com/v1.0/me/onenote/sections/{brain_section_id}/pages"
            pages_response = requests.get(pages_url, headers=headers, timeout=60)
            
            if pages_response.status_code != 200:
                self.log_error(f"Failed to get pages: {pages_response.status_code}")
                return []
            
            pages = pages_response.json().get('value', [])
            self.log_progress(f"✅ Found {len(pages)} pages in Brain section")
            
            # Get content for each page
            all_content = []
            
            for i, page in enumerate(pages, 1):
                page_title = page.get('title', f'Page {i}')
                page_id = page.get('id')
                
                self.log_progress(f"📄 [{i}/{len(pages)}] Getting content: {page_title}")
                
                # Get page content
                content_url = f"https://graph.microsoft.com/v1.0/me/onenote/pages/{page_id}/content"
                content_response = requests.get(content_url, headers=headers, timeout=60)
                
                if content_response.status_code == 200:
                    html_content = content_response.text
                    
                    # Extract text from HTML
                    soup = BeautifulSoup(html_content, 'html.parser')
                    for script in soup(["script", "style"]):
                        script.decompose()
                    
                    text = soup.get_text()
                    lines = (line.strip() for line in text.splitlines())
                    chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                    clean_text = ' '.join(chunk for chunk in chunks if chunk)
                    
                    all_content.append({
                        'page_title': page_title,
                        'page_id': page_id,
                        'content': clean_text,
                        'content_length': len(clean_text)
                    })
                    
                    self.log_progress(f"✅ {page_title}: {len(clean_text)} characters")
                    
                    # Special attention to ginger page
                    if 'ginger' in page_title.lower():
                        self.log_progress(f"🌶️ GINGER PAGE: {len(clean_text)} characters extracted!")
                else:
                    self.log_error(f"Failed to get content for {page_title}: {content_response.status_code}")
                
                # Brief pause to avoid rate limiting
                await asyncio.sleep(0.5)
            
            return all_content
            
        except Exception as e:
            self.log_error(f"Error getting Brain content: {e}")
            return []
    
    async def process_content_chunk(self, content, metadata):
        """Process a single content chunk through the full pipeline."""
        try:
            chunk_id = f"{metadata['page_title']}_{metadata.get('chunk_index', 0)}"
            self.log_progress(f"🔄 Processing chunk: {chunk_id}")

            # Extract entities using the entity extraction client
            entities_result = await self.entity_client.extract_entities(content)
            entities = entities_result.get('entities', []) if entities_result else []
            self.log_progress(f"🧠 Extracted {len(entities)} entities")

            # Extract references using simple regex patterns
            references = self.extract_references_simple(content)
            self.log_progress(f"📚 Extracted {len(references)} references")

            # Generate embeddings
            embeddings = await self.embedding_utils.generate_embeddings(content)
            self.log_progress(f"🔍 Generated embeddings")

            # Store entities in FalkorDB
            for entity in entities:
                try:
                    await self.falkor_db.add_entity(
                        entity_type=entity.get('type', 'Unknown'),
                        entity_name=entity.get('name', ''),
                        properties=entity.get('properties', {}),
                        metadata=metadata
                    )
                except Exception as e:
                    self.log_error(f"Error storing entity {entity.get('name', '')}: {e}")

            # Store references
            for reference in references:
                try:
                    await self.reference_adapter.add_reference(
                        reference_text=reference.get('text', ''),
                        reference_type=reference.get('type', 'unknown'),
                        metadata={**metadata, **reference}
                    )
                except Exception as e:
                    self.log_error(f"Error storing reference: {e}")

            # Store embeddings in Redis vector database
            if embeddings:
                try:
                    await self.vector_search.store_embedding(
                        text=content,
                        embedding=embeddings,
                        metadata=metadata
                    )
                except Exception as e:
                    self.log_error(f"Error storing embeddings: {e}")

            # Update stats
            self.stats['chunks_processed'] += 1
            self.stats['entities_added'] += len(entities)
            self.stats['references_added'] += len(references)
            self.stats['embeddings_generated'] += 1 if embeddings else 0

            self.log_progress(f"✅ Chunk processed: {len(entities)}E, {len(references)}R, {'1' if embeddings else '0'}Em")

            return True

        except Exception as e:
            self.log_error(f"Error processing chunk {chunk_id}: {e}")
            return False

    def extract_references_simple(self, text):
        """Simple reference extraction using regex patterns."""
        import re
        references = []

        # DOI pattern
        doi_pattern = r'doi:\s*(10\.\d+/[^\s]+)'
        for doi in re.findall(doi_pattern, text, re.IGNORECASE):
            references.append({'type': 'doi', 'doi': doi, 'text': f"DOI: {doi}"})

        # PMID pattern
        pmid_pattern = r'PMID:\s*(\d+)'
        for pmid in re.findall(pmid_pattern, text, re.IGNORECASE):
            references.append({'type': 'pmid', 'pmid': pmid, 'text': f"PMID: {pmid}"})

        # Numbered references
        numbered_pattern = r'^\s*(\d+)\.\s+(.+?)(?=\n\s*\d+\.|$)'
        for num, ref_text in re.findall(numbered_pattern, text, re.MULTILINE | re.DOTALL):
            references.append({'type': 'numbered', 'number': int(num), 'text': ref_text.strip()})

        return references
    
    async def chunk_content(self, content, chunk_size=1200, overlap=0):
        """Split content into chunks for processing."""
        if len(content) <= chunk_size:
            return [content]
        
        chunks = []
        start = 0
        
        while start < len(content):
            end = start + chunk_size
            
            # Try to break at sentence boundary
            if end < len(content):
                # Look for sentence ending within last 100 characters
                sentence_end = content.rfind('.', end - 100, end)
                if sentence_end > start:
                    end = sentence_end + 1
            
            chunk = content[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = end - overlap
        
        return chunks
    
    async def process_all_content(self):
        """Process all OneNote Brain content through the full pipeline."""
        self.start_time = time.time()
        
        self.log_progress("🚀 Starting Automated OneNote Brain Ingestion")
        self.log_progress(f"📊 Current entity count in dashboard: 13,748")
        
        # Ensure authentication
        if not await self.ensure_authentication():
            return False
        
        # Get all content
        all_content = await self.get_brain_content()
        
        if not all_content:
            self.log_error("No content found to process")
            return False
        
        self.log_progress(f"📋 Processing {len(all_content)} pages through full pipeline...")
        
        # Process each page
        for page_data in all_content:
            page_title = page_data['page_title']
            content = page_data['content']
            
            self.log_progress(f"📄 Processing page: {page_title}")
            
            # Chunk the content
            chunks = await self.chunk_content(content, chunk_size=1200, overlap=0)
            self.log_progress(f"📋 Split into {len(chunks)} chunks")
            
            # Process each chunk
            for i, chunk in enumerate(chunks):
                metadata = {
                    'source': 'OneNote',
                    'notebook': 'Biochemistry',
                    'section': 'Brain',
                    'page_title': page_title,
                    'page_id': page_data['page_id'],
                    'chunk_index': i,
                    'total_chunks': len(chunks)
                }
                
                success = await self.process_content_chunk(chunk, metadata)
                
                if success:
                    self.stats['pages_processed'] += 1 if i == 0 else 0  # Count page once
                
                # Brief pause between chunks
                await asyncio.sleep(0.2)
        
        return True
    
    async def generate_final_report(self):
        """Generate final processing report."""
        elapsed = time.time() - self.start_time if self.start_time else 0
        
        print("\n" + "=" * 60)
        print("📊 AUTOMATED INGESTION COMPLETE")
        print("=" * 60)
        print(f"⏰ Total processing time: {elapsed:.1f} seconds")
        print(f"📄 Pages processed: {self.stats['pages_processed']}")
        print(f"📋 Chunks processed: {self.stats['chunks_processed']}")
        print(f"🧠 Entities added to knowledge graph: {self.stats['entities_added']}")
        print(f"📚 References added: {self.stats['references_added']}")
        print(f"🔍 Embeddings generated: {self.stats['embeddings_generated']}")
        print(f"❌ Errors encountered: {len(self.stats['errors'])}")
        
        if self.stats['errors']:
            print(f"\n❌ Error Details:")
            for i, error in enumerate(self.stats['errors'], 1):
                print(f"   {i}. {error}")
        
        print(f"\n📈 Entity Count Change:")
        print(f"   Before: 13,748 entities")
        print(f"   Added: {self.stats['entities_added']} entities")
        print(f"   Expected After: {13748 + self.stats['entities_added']} entities")
        print(f"   📋 CHECK DASHBOARD NOW for actual count!")
        
        # Save detailed results
        results_file = "automated_ingestion_results.json"
        try:
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, indent=2, ensure_ascii=False)
            print(f"\n💾 Detailed results saved to: {results_file}")
        except Exception as e:
            print(f"⚠️ Could not save results: {e}")

async def main():
    """Main automated ingestion function."""
    print("🌟" * 60)
    print("🤖 AUTOMATED ONENOTE BRAIN INGESTION PIPELINE")
    print("🌟" * 60)
    
    # Check database connections
    try:
        import redis
        redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        redis_client.ping()
        print("✅ Redis connection: Working")
        
        falkor_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        falkor_client.execute_command("GRAPH.QUERY", "knowledge_graph", "RETURN 1")
        print("✅ FalkorDB connection: Working")
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("📋 Please start Redis/FalkorDB: docker-compose up -d")
        return
    
    # Initialize and run pipeline
    pipeline = AutomatedIngestionPipeline()
    
    success = await pipeline.process_all_content()
    
    await pipeline.generate_final_report()
    
    if success:
        print("\n🎉 SUCCESS! Automated ingestion completed!")
        print("\n📋 Your OneNote Brain content is now in the knowledge graph!")
        print("📊 Check the dashboard for updated entity count")
        print("🔍 Test search and Q&A with the new content")
    else:
        print("\n❌ Automated ingestion failed")
        print("📋 Check errors above and retry")
    
    print("\n🌟" * 60)
    print("🎉 AUTOMATED PIPELINE COMPLETE!")
    print("🌟" * 60)

if __name__ == "__main__":
    asyncio.run(main())
