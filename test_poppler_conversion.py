#!/usr/bin/env python3
"""Test Poppler PDF to image conversion."""

import os
import tempfile
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter

def create_test_pdf():
    """Create a simple test PDF."""
    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_pdf:
        pdf_path = temp_pdf.name
    
    c = canvas.Canvas(pdf_path, pagesize=letter)
    c.setFont("Helvetica", 12)
    
    # Add some test content with references
    content = [
        "Test Document for OCR",
        "",
        "This is a test document to verify PDF to image conversion.",
        "",
        "References:",
        "1. <PERSON>, <PERSON>. (2020). Test paper. Journal of Testing, 1(1), 1-10.",
        "2. <PERSON>, A. (2021). Another test. Science Today, 2(2), 20-30.",
        "DOI: 10.1000/test.2021.001",
        "PMID: 12345678"
    ]
    
    y_position = 750
    for line in content:
        c.drawString(50, y_position, line)
        y_position -= 20
    
    c.save()
    print(f"✅ Test PDF created: {pdf_path}")
    return pdf_path

def test_pdf2image():
    """Test pdf2image with our Poppler installation."""
    try:
        import pdf2image
        
        # Create test PDF
        pdf_path = create_test_pdf()
        
        # Set up Poppler path
        poppler_path = os.path.join(os.getcwd(), "poppler", "poppler-23.01.0", "Library", "bin")
        print(f"📁 Using Poppler path: {poppler_path}")
        
        # Convert PDF to images
        print("🔄 Converting PDF to images...")
        images = pdf2image.convert_from_path(
            pdf_path,
            dpi=300,
            poppler_path=poppler_path
        )
        
        print(f"✅ Successfully converted PDF to {len(images)} images")
        
        # Save first image as test
        if images:
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_img:
                images[0].save(temp_img.name, 'PNG')
                img_size = os.path.getsize(temp_img.name)
                print(f"✅ Test image saved: {temp_img.name} ({img_size} bytes)")
                
                # Clean up
                os.unlink(temp_img.name)
        
        # Clean up
        os.unlink(pdf_path)
        
        return True
        
    except Exception as e:
        print(f"❌ Error in pdf2image test: {e}")
        return False

def test_pymupdf():
    """Test PyMuPDF as alternative."""
    try:
        import fitz
        
        # Create test PDF
        pdf_path = create_test_pdf()
        
        print("🔄 Testing PyMuPDF conversion...")
        doc = fitz.open(pdf_path)
        
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2x zoom
            
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_img:
                pix.save(temp_img.name)
                img_size = os.path.getsize(temp_img.name)
                print(f"✅ PyMuPDF image saved: page {page_num+1} ({img_size} bytes)")
                
                # Clean up
                os.unlink(temp_img.name)
        
        doc.close()
        os.unlink(pdf_path)
        
        return True
        
    except Exception as e:
        print(f"❌ Error in PyMuPDF test: {e}")
        return False

def main():
    """Main test function."""
    print("🔧 Testing PDF to Image Conversion")
    print("=" * 40)
    
    # Test pdf2image with Poppler
    print("\n📄 Testing pdf2image with Poppler:")
    pdf2image_ok = test_pdf2image()
    
    # Test PyMuPDF as alternative
    print("\n📄 Testing PyMuPDF:")
    pymupdf_ok = test_pymupdf()
    
    # Summary
    print(f"\n📊 RESULTS:")
    print(f"   pdf2image + Poppler: {'✅' if pdf2image_ok else '❌'}")
    print(f"   PyMuPDF: {'✅' if pymupdf_ok else '❌'}")
    
    if pdf2image_ok or pymupdf_ok:
        print(f"\n🎉 PDF to image conversion is working!")
        print(f"📋 Ready for OCR processing once qwen2.5vl:3b is available.")
    else:
        print(f"\n❌ PDF to image conversion failed")

if __name__ == "__main__":
    main()
