#!/usr/bin/env python3
"""
Direct OneNote Brain Processing

This script uses the direct OneNote API approach that we know works,
bypassing the LangChain loader that's having timeout issues.
"""

import asyncio
import sys
import os
import time
import logging
import json
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.onenote_auth_manager import OneNoteAuthManager
from processors.enhanced_document_processor import EnhancedDocumentProcessor

# Set up detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DirectOneNoteProcessor:
    """Direct OneNote processing using Graph API."""
    
    def __init__(self):
        self.auth_manager = OneNoteAuthManager()
        self.doc_processor = EnhancedDocumentProcessor()
        self.start_time = None
        self.stats = {
            'pages_processed': 0,
            'entities_extracted': 0,
            'references_extracted': 0,
            'embeddings_generated': 0,
            'errors': []
        }
        
    def log_progress(self, message):
        """Log progress with timestamp."""
        elapsed = time.time() - self.start_time if self.start_time else 0
        print(f"[{elapsed:.1f}s] {message}")
        
    def log_error(self, error_msg):
        """Log an error."""
        self.stats['errors'].append(error_msg)
        elapsed = time.time() - self.start_time if self.start_time else 0
        print(f"[{elapsed:.1f}s] ❌ ERROR: {error_msg}")
        
    async def get_brain_pages(self):
        """Get all pages from the Brain section."""
        try:
            import requests
            from bs4 import BeautifulSoup
            
            access_token = self.auth_manager.get_valid_token()
            if not access_token:
                self.log_error("No valid access token")
                return []
            
            headers = {'Authorization': f'Bearer {access_token}'}
            
            # Get the Brain section
            self.log_progress("🔍 Finding Brain section...")
            sections_url = "https://graph.microsoft.com/v1.0/me/onenote/sections?$filter=displayName eq 'Brain'"
            sections_response = requests.get(sections_url, headers=headers, timeout=30)
            
            if sections_response.status_code != 200:
                self.log_error(f"Failed to get Brain section: {sections_response.status_code}")
                return []
            
            sections = sections_response.json().get('value', [])
            if not sections:
                self.log_error("Brain section not found")
                return []
            
            brain_section_id = sections[0]['id']
            self.log_progress(f"✅ Found Brain section: {brain_section_id}")
            
            # Get pages in the Brain section
            self.log_progress("📄 Getting pages from Brain section...")
            pages_url = f"https://graph.microsoft.com/v1.0/me/onenote/sections/{brain_section_id}/pages"
            pages_response = requests.get(pages_url, headers=headers, timeout=60)
            
            if pages_response.status_code != 200:
                self.log_error(f"Failed to get pages: {pages_response.status_code}")
                return []
            
            pages = pages_response.json().get('value', [])
            self.log_progress(f"✅ Found {len(pages)} pages in Brain section")
            
            return pages
            
        except Exception as e:
            self.log_error(f"Error getting Brain pages: {e}")
            return []
    
    async def process_page(self, page):
        """Process a single OneNote page."""
        try:
            import requests
            from bs4 import BeautifulSoup
            
            page_title = page.get('title', 'Unknown')
            page_id = page.get('id')
            
            self.log_progress(f"📄 Processing page: {page_title}")
            
            access_token = self.auth_manager.get_valid_token()
            headers = {'Authorization': f'Bearer {access_token}'}
            
            # Get the page content
            content_url = f"https://graph.microsoft.com/v1.0/me/onenote/pages/{page_id}/content"
            content_response = requests.get(content_url, headers=headers, timeout=60)
            
            if content_response.status_code != 200:
                self.log_error(f"Failed to get content for {page_title}: {content_response.status_code}")
                return None
            
            html_content = content_response.text
            
            # Extract text from HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text
            text = soup.get_text()
            
            # Clean up whitespace
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            self.log_progress(f"✅ Extracted {len(text)} characters from {page_title}")
            
            # Create temporary file for processing
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
                temp_file.write(text)
                temp_file_path = temp_file.name

            try:
                # Process with document processor
                result = await self.doc_processor.process_document(
                    file_path=temp_file_path,
                    chunk_size=1200,
                    overlap=0,
                    extract_entities=True,
                    extract_references=True,
                    extract_metadata=True,
                    generate_embeddings=True
                )
            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_file_path)
                except:
                    pass
            
            if result.get('success', False):
                entities = result.get('entities_extracted', 0)
                references = result.get('references_extracted', 0)
                embeddings = result.get('embeddings_generated', 0)
                
                self.stats['pages_processed'] += 1
                self.stats['entities_extracted'] += entities
                self.stats['references_extracted'] += references
                self.stats['embeddings_generated'] += embeddings
                
                self.log_progress(f"✅ {page_title}: E={entities}, R={references}, Em={embeddings}")
                
                # Special attention to ginger page
                if 'ginger' in page_title.lower():
                    self.log_progress(f"🌶️ GINGER PAGE: Found {references} references!")
                
                return result
            else:
                error = result.get('error', 'Unknown error')
                self.log_error(f"Processing failed for {page_title}: {error}")
                return None
                
        except Exception as e:
            self.log_error(f"Error processing page {page_title}: {e}")
            return None
    
    async def process_all_pages(self):
        """Process all pages in the Brain section."""
        self.start_time = time.time()
        
        self.log_progress("🧠 Starting Direct OneNote Brain Processing")
        self.log_progress(f"📊 Current entity count in dashboard: 13,748")
        
        # Get all pages
        pages = await self.get_brain_pages()
        
        if not pages:
            self.log_error("No pages found to process")
            return False
        
        self.log_progress(f"📋 Processing {len(pages)} pages...")
        
        # Process each page
        successful_pages = 0
        failed_pages = 0
        
        for i, page in enumerate(pages, 1):
            page_title = page.get('title', f'Page {i}')
            self.log_progress(f"📄 [{i}/{len(pages)}] Processing: {page_title}")
            
            result = await self.process_page(page)
            
            if result:
                successful_pages += 1
            else:
                failed_pages += 1
            
            # Brief pause between pages to avoid rate limiting
            await asyncio.sleep(1)
        
        # Final report
        elapsed = time.time() - self.start_time
        
        print("\n" + "=" * 60)
        print("📊 FINAL PROCESSING REPORT")
        print("=" * 60)
        print(f"⏰ Total processing time: {elapsed:.1f} seconds")
        print(f"📄 Pages found: {len(pages)}")
        print(f"📄 Pages processed successfully: {successful_pages}")
        print(f"📄 Pages failed: {failed_pages}")
        print(f"🧠 Entities extracted: {self.stats['entities_extracted']}")
        print(f"📚 References extracted: {self.stats['references_extracted']}")
        print(f"🔍 Embeddings generated: {self.stats['embeddings_generated']}")
        print(f"❌ Errors encountered: {len(self.stats['errors'])}")
        
        if self.stats['errors']:
            print(f"\n❌ Error Details:")
            for i, error in enumerate(self.stats['errors'], 1):
                print(f"   {i}. {error}")
        
        print(f"\n📈 Entity Count Change:")
        print(f"   Before: 13,748 entities")
        print(f"   Added: {self.stats['entities_extracted']} entities")
        print(f"   Expected After: {13748 + self.stats['entities_extracted']} entities")
        print(f"   📋 Check dashboard to verify actual count")
        
        return successful_pages > 0

async def main():
    """Main processing function."""
    print("🌟" * 60)
    print("🧠 Direct OneNote Brain Processing")
    print("🌟" * 60)
    
    # Check database connections
    try:
        import redis
        redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        redis_client.ping()
        print("✅ Redis connection: Working")
        
        falkor_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        falkor_client.execute_command("GRAPH.QUERY", "knowledge_graph", "RETURN 1")
        print("✅ FalkorDB connection: Working")
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("📋 Please start Redis/FalkorDB before processing")
        return
    
    # Initialize processor
    processor = DirectOneNoteProcessor()
    
    # Process all pages
    success = await processor.process_all_pages()
    
    if success:
        print("\n🎉 SUCCESS! OneNote Brain section processed!")
        print("\n📋 Next steps:")
        print("   1. Check the dashboard for updated entity count")
        print("   2. Verify new entities in the knowledge graph")
        print("   3. Test search functionality with new content")
        print("   4. Review extracted references")
    else:
        print("\n❌ Processing failed - check errors above")
    
    print("\n🌟" * 60)
    print("🎉 Processing Complete!")
    print("🌟" * 60)

if __name__ == "__main__":
    asyncio.run(main())
