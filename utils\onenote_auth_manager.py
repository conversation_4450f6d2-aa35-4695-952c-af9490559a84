#!/usr/bin/env python3
"""
OneNote Authentication Manager

This module handles Microsoft Graph API authentication and token management
for OneNote access, providing a bridge between our authentication system
and the LangChain OneNote loader.
"""

import os
import json
import time
import logging
from pathlib import Path
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

try:
    import msal
    MSAL_AVAILABLE = True
except ImportError:
    MSAL_AVAILABLE = False
    logger.warning("msal library not available")

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass


class OneNoteAuthManager:
    """
    Manages Microsoft Graph API authentication and token storage for OneNote access.
    
    This class handles:
    - Interactive OAuth authentication
    - Token storage and retrieval
    - Token refresh
    - Integration with LangChain OneNote loader
    """
    
    def __init__(self):
        """Initialize the authentication manager."""
        self.client_id = os.getenv('MS_GRAPH_CLIENT_ID')
        self.client_secret = os.getenv('MS_GRAPH_CLIENT_SECRET')
        self.redirect_uri = "http://localhost:8000/callback"
        self.scopes = ["https://graph.microsoft.com/Notes.Read"]
        
        # Token storage path
        self.credentials_dir = Path.home() / ".credentials"
        self.token_file = self.credentials_dir / "onenote_graph_token.txt"
        
        # Ensure credentials directory exists
        self.credentials_dir.mkdir(exist_ok=True)
        
        if not self.client_id or not self.client_secret:
            logger.error("Microsoft Graph API credentials not configured")
    
    def is_configured(self) -> bool:
        """Check if authentication is properly configured."""
        return bool(self.client_id and self.client_secret and MSAL_AVAILABLE)
    
    def get_msal_app(self):
        """Get MSAL application instance."""
        if not self.is_configured():
            raise ValueError("Authentication not properly configured")
        
        return msal.ConfidentialClientApplication(
            client_id=self.client_id,
            client_credential=self.client_secret,
            authority="https://login.microsoftonline.com/consumers"
        )
    
    def load_token_cache(self) -> Optional[Dict[str, Any]]:
        """Load token from cache file."""
        try:
            if self.token_file.exists():
                with open(self.token_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()

                # Try to parse as JSON first
                try:
                    token_data = json.loads(content)
                except json.JSONDecodeError:
                    # If not JSON, assume it's a raw token string
                    token_data = {
                        'access_token': content,
                        'expires_at': time.time() + 3600  # Assume 1 hour validity
                    }

                # Check if token is still valid
                if token_data.get('expires_at', 0) > time.time() + 300:  # 5 min buffer
                    logger.info("Valid token found in cache")
                    return token_data
                else:
                    logger.info("Token expired, will need to refresh")
                    return token_data
            else:
                logger.info("No token cache found")
                return None
        except Exception as e:
            logger.error(f"Error loading token cache: {e}")
            return None
    
    def save_token_cache(self, token_data: Dict[str, Any]):
        """Save token to cache file."""
        try:
            # Add expiration timestamp
            if 'expires_in' in token_data:
                token_data['expires_at'] = time.time() + token_data['expires_in']
            
            with open(self.token_file, 'w', encoding='utf-8') as f:
                json.dump(token_data, f, indent=2)
            
            logger.info("Token saved to cache")
        except Exception as e:
            logger.error(f"Error saving token cache: {e}")
    
    def get_auth_url(self) -> str:
        """Get the authorization URL for interactive authentication."""
        if not self.is_configured():
            raise ValueError("Authentication not properly configured")
        
        app = self.get_msal_app()
        auth_url = app.get_authorization_request_url(
            scopes=self.scopes,
            redirect_uri=self.redirect_uri
        )
        return auth_url
    
    def authenticate_with_code(self, authorization_code: str) -> Dict[str, Any]:
        """
        Complete authentication using authorization code from redirect.
        
        Args:
            authorization_code: The authorization code from the redirect URL
            
        Returns:
            Token data dictionary
        """
        if not self.is_configured():
            raise ValueError("Authentication not properly configured")
        
        app = self.get_msal_app()
        
        try:
            result = app.acquire_token_by_authorization_code(
                code=authorization_code,
                scopes=self.scopes,
                redirect_uri=self.redirect_uri
            )
            
            if "access_token" in result:
                logger.info("Authentication successful")
                self.save_token_cache(result)
                return result
            else:
                error = result.get("error", "Unknown error")
                error_desc = result.get("error_description", "No description")
                raise ValueError(f"Authentication failed: {error} - {error_desc}")
                
        except Exception as e:
            logger.error(f"Error during authentication: {e}")
            raise
    
    def refresh_token(self, refresh_token: str) -> Optional[Dict[str, Any]]:
        """
        Refresh access token using refresh token.
        
        Args:
            refresh_token: The refresh token
            
        Returns:
            New token data or None if refresh failed
        """
        if not self.is_configured():
            return None
        
        app = self.get_msal_app()
        
        try:
            result = app.acquire_token_by_refresh_token(
                refresh_token=refresh_token,
                scopes=self.scopes
            )
            
            if "access_token" in result:
                logger.info("Token refreshed successfully")
                self.save_token_cache(result)
                return result
            else:
                logger.warning("Token refresh failed")
                return None
                
        except Exception as e:
            logger.error(f"Error refreshing token: {e}")
            return None
    
    def get_valid_token(self) -> Optional[str]:
        """
        Get a valid access token, refreshing if necessary.
        
        Returns:
            Valid access token or None if authentication required
        """
        token_data = self.load_token_cache()
        
        if not token_data:
            logger.info("No token available, authentication required")
            return None
        
        # Check if token is still valid
        if token_data.get('expires_at', 0) > time.time() + 300:  # 5 min buffer
            return token_data.get('access_token')
        
        # Try to refresh token
        refresh_token = token_data.get('refresh_token')
        if refresh_token:
            logger.info("Attempting to refresh token")
            new_token_data = self.refresh_token(refresh_token)
            if new_token_data:
                return new_token_data.get('access_token')
        
        logger.info("Token refresh failed, authentication required")
        return None
    
    def interactive_authenticate(self) -> bool:
        """
        Perform interactive authentication flow.
        
        Returns:
            True if authentication successful, False otherwise
        """
        try:
            print("🔐 Starting interactive authentication...")
            print("📋 You will need to:")
            print("   1. Visit the URL that will be displayed")
            print("   2. Sign in with your Microsoft account")
            print("   3. Grant permissions")
            print("   4. Copy the redirect URL back here")
            print()
            
            # Get authorization URL
            auth_url = self.get_auth_url()
            print(f"🌐 Visit this URL to authenticate:")
            print(f"   {auth_url}")
            print()
            
            # Get authorization code from user
            redirect_url = input("📋 Paste the redirect URL here: ").strip()
            
            # Extract authorization code from URL
            if "code=" not in redirect_url:
                print("❌ Invalid redirect URL - no authorization code found")
                return False
            
            # Parse authorization code
            import urllib.parse
            parsed_url = urllib.parse.urlparse(redirect_url)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            
            if 'code' not in query_params:
                print("❌ No authorization code found in URL")
                return False
            
            auth_code = query_params['code'][0]
            
            # Complete authentication
            token_data = self.authenticate_with_code(auth_code)
            
            if token_data and 'access_token' in token_data:
                print("✅ Authentication successful!")
                return True
            else:
                print("❌ Authentication failed")
                return False
                
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False
    
    def create_langchain_token_file(self) -> bool:
        """
        Create a token file compatible with LangChain OneNote loader.

        Returns:
            True if token file created successfully, False otherwise
        """
        try:
            access_token = self.get_valid_token()

            if not access_token:
                logger.error("No valid access token available")
                return False

            # Create token file in the format expected by LangChain (just the access token)
            langchain_token_file = self.credentials_dir / "langchain_onenote_token.txt"

            with open(langchain_token_file, 'w', encoding='utf-8') as f:
                f.write(access_token)

            logger.info(f"LangChain-compatible token file created: {langchain_token_file}")
            return True

        except Exception as e:
            logger.error(f"Error creating LangChain token file: {e}")
            return False
