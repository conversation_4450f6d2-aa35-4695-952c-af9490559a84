#!/usr/bin/env python3
"""Test existing models for text processing capabilities."""

import requests
import json

def test_model_text_processing(model_name):
    """Test a model's text processing capabilities."""
    test_text = """
    Test Document with References
    
    This is a test document for text processing.
    
    References:
    1. <PERSON>, <PERSON> (2020). Test paper about ginger. Journal of Testing, 45(3), 123-135.
    2. <PERSON>, <PERSON> (2019). Another study on neuroprotection. Nature Reviews, 32(8), 456-467.
    DOI: 10.1000/test.2020.001
    PMID: 12345678
    """
    
    prompt = f"""Please extract all references, DOIs, and PMIDs from the following text. Format them as a list:

{test_text}

Extract:
- All numbered references
- All DOI numbers
- All PMID numbers
"""
    
    try:
        response = requests.post(
            'http://localhost:11434/api/generate',
            json={
                'model': model_name,
                'prompt': prompt,
                'stream': False
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            extracted = result.get('response', '')
            
            # Check if it found the references
            found_items = []
            if 'Smith' in extracted:
                found_items.append('Reference 1')
            if 'Johnson' in extracted:
                found_items.append('Reference 2')
            if '10.1000' in extracted:
                found_items.append('DOI')
            if '12345678' in extracted:
                found_items.append('PMID')
            
            return {
                'success': True,
                'response': extracted,
                'found_items': found_items,
                'score': len(found_items)
            }
        else:
            return {
                'success': False,
                'error': f"Status {response.status_code}",
                'score': 0
            }
            
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'score': 0
        }

def main():
    """Test existing models for text processing."""
    print("🧪 Testing Existing Models for Reference Extraction")
    print("=" * 55)
    
    # Get available models
    try:
        response = requests.get('http://localhost:11434/api/tags', timeout=10)
        if response.status_code == 200:
            models = [m['name'] for m in response.json().get('models', [])]
            print(f"📊 Found {len(models)} models to test")
        else:
            print(f"❌ Could not get model list: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Error getting models: {e}")
        return
    
    # Test each model
    results = []
    
    for model in models:
        print(f"\n🔄 Testing {model}...")
        result = test_model_text_processing(model)
        
        if result['success']:
            score = result['score']
            found = result['found_items']
            print(f"   ✅ Success! Score: {score}/4")
            print(f"   📋 Found: {', '.join(found) if found else 'None'}")
            
            if score >= 3:
                print(f"   🎉 Excellent reference extraction!")
            elif score >= 2:
                print(f"   👍 Good reference extraction!")
            elif score >= 1:
                print(f"   ⚠️ Basic reference extraction")
            else:
                print(f"   ❌ Poor reference extraction")
                
            results.append((model, score, result))
        else:
            print(f"   ❌ Failed: {result['error']}")
            results.append((model, 0, result))
    
    # Summary
    print(f"\n📊 RESULTS SUMMARY:")
    print("=" * 30)
    
    # Sort by score
    results.sort(key=lambda x: x[1], reverse=True)
    
    for model, score, result in results:
        status = "✅" if score >= 2 else "⚠️" if score >= 1 else "❌"
        print(f"   {status} {model}: {score}/4")
    
    # Best model
    if results and results[0][1] > 0:
        best_model, best_score, best_result = results[0]
        print(f"\n🏆 Best Model: {best_model} (Score: {best_score}/4)")
        
        if best_score >= 2:
            print(f"📋 This model can be used for reference extraction!")
            print(f"💡 Consider using {best_model} as an alternative to qwen2.5vl:3b")
            
            # Show sample output
            sample = best_result['response'][:300]
            print(f"\n📄 Sample output from {best_model}:")
            print(f"   {sample}...")
    else:
        print(f"\n❌ No models performed well on reference extraction")
    
    print(f"\n💡 Note: These models process text directly, not images")
    print(f"   For OneNote content, we already extract text successfully")
    print(f"   So we can use the best text model for reference extraction!")

if __name__ == "__main__":
    main()
