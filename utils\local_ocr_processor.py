#!/usr/bin/env python3
"""
Local OCR Processor using Ollama qwen2.5vl:3b

This module provides local OCR capabilities using the qwen2.5vl:3b vision model
for processing PDFs and extracting text content, especially references.
"""

import os
import logging
import tempfile
from pathlib import Path
from typing import Optional, Dict, Any, List
import base64
import json

logger = logging.getLogger(__name__)

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    logger.error("requests library not available")

try:
    from PIL import Image
    import pdf2image
    PDF2IMAGE_AVAILABLE = True
except ImportError:
    PDF2IMAGE_AVAILABLE = False
    logger.warning("pdf2image or PIL not available for PDF to image conversion")

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    logger.warning("PyMuPDF not available for PDF processing")


class LocalOCRProcessor:
    """
    Local OCR processor using Ollama qwen2.5vl:3b vision model.
    """
    
    def __init__(self, ollama_base_url: str = "http://localhost:11434"):
        """
        Initialize the local OCR processor.
        
        Args:
            ollama_base_url: Base URL for Ollama API
        """
        self.ollama_base_url = ollama_base_url
        self.model_name = "qwen2.5vl:3b"
        self.available = self._check_availability()
        
        if self.available:
            logger.info(f"✅ Local OCR processor initialized with {self.model_name}")
        else:
            logger.warning("⚠️ Local OCR processor not available")
    
    def _check_availability(self) -> bool:
        """Check if Ollama and the vision model are available."""
        if not REQUESTS_AVAILABLE:
            return False

        try:
            # Check if Ollama is running
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            if response.status_code != 200:
                logger.warning("Ollama API not accessible")
                return False

            # Check if qwen2.5vl:3b is available
            models = response.json().get('models', [])
            model_names = [model.get('name', '') for model in models]

            if self.model_name not in model_names:
                logger.warning(f"Model {self.model_name} not found in model list")
                logger.info(f"Available models: {model_names}")

                # Try to test the model directly (sometimes models work but don't show in list)
                logger.info(f"Testing model {self.model_name} directly...")
                test_payload = {
                    "model": self.model_name,
                    "prompt": "Test",
                    "stream": False
                }

                test_response = requests.post(
                    f"{self.ollama_base_url}/api/generate",
                    json=test_payload,
                    timeout=10
                )

                if test_response.status_code == 200:
                    logger.info(f"✅ Model {self.model_name} is working despite not being in list")
                    return True
                else:
                    logger.warning(f"Model {self.model_name} test failed: {test_response.status_code}")
                    return False

            return True

        except Exception as e:
            logger.error(f"Error checking Ollama availability: {e}")
            return False
    
    def is_available(self) -> bool:
        """Check if the OCR processor is available."""
        return self.available
    
    def pdf_to_images(self, pdf_path: str) -> List[str]:
        """
        Convert PDF to images for OCR processing.

        Args:
            pdf_path: Path to PDF file

        Returns:
            List of image file paths
        """
        image_paths = []

        try:
            if PDF2IMAGE_AVAILABLE:
                # Set up Poppler path
                poppler_path = os.path.join(os.getcwd(), "poppler", "poppler-23.01.0", "Library", "bin")

                # Use pdf2image (requires poppler)
                images = pdf2image.convert_from_path(
                    pdf_path,
                    dpi=300,
                    poppler_path=poppler_path
                )

                for i, image in enumerate(images):
                    temp_image = tempfile.NamedTemporaryFile(
                        suffix=f'_page_{i+1}.png', delete=False
                    )
                    image.save(temp_image.name, 'PNG')
                    image_paths.append(temp_image.name)
                    temp_image.close()

                logger.info(f"✅ Converted PDF to {len(image_paths)} images using pdf2image")

            elif PYMUPDF_AVAILABLE:
                # Use PyMuPDF as fallback
                doc = fitz.open(pdf_path)
                
                for page_num in range(len(doc)):
                    page = doc.load_page(page_num)
                    pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2x zoom for better quality
                    
                    temp_image = tempfile.NamedTemporaryFile(
                        suffix=f'_page_{page_num+1}.png', delete=False
                    )
                    pix.save(temp_image.name)
                    image_paths.append(temp_image.name)
                    temp_image.close()
                
                doc.close()
                logger.info(f"✅ Converted PDF to {len(image_paths)} images using PyMuPDF")
                
            else:
                logger.error("❌ No PDF to image conversion library available")
                
        except Exception as e:
            logger.error(f"❌ Error converting PDF to images: {e}")
        
        return image_paths
    
    def image_to_base64(self, image_path: str) -> Optional[str]:
        """
        Convert image to base64 for API transmission.
        
        Args:
            image_path: Path to image file
            
        Returns:
            Base64 encoded image string
        """
        try:
            with open(image_path, 'rb') as image_file:
                encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
                return encoded_string
        except Exception as e:
            logger.error(f"❌ Error encoding image to base64: {e}")
            return None
    
    def extract_text_from_image(self, image_path: str, focus_on_references: bool = True) -> Optional[str]:
        """
        Extract text from image using qwen2.5vl:3b.
        
        Args:
            image_path: Path to image file
            focus_on_references: Whether to focus on extracting references
            
        Returns:
            Extracted text or None if failed
        """
        if not self.available:
            return None
        
        try:
            # Convert image to base64
            image_base64 = self.image_to_base64(image_path)
            if not image_base64:
                return None
            
            # Prepare prompt based on focus
            if focus_on_references:
                prompt = """Please extract all text from this image, paying special attention to:
1. References and citations (numbered or author-year format)
2. Bibliography sections
3. DOI numbers, PubMed IDs, journal names
4. Author names and publication years
5. Any numbered lists that might be references

Please provide the complete text content, maintaining the original structure and formatting as much as possible. Focus especially on capturing all reference information accurately."""
            else:
                prompt = """Please extract all text from this image, maintaining the original structure and formatting as much as possible. Provide a complete and accurate transcription of all visible text."""
            
            # Prepare API request
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "images": [image_base64],
                "stream": False
            }
            
            # Make API request
            response = requests.post(
                f"{self.ollama_base_url}/api/generate",
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                extracted_text = result.get('response', '')
                
                if extracted_text:
                    logger.info(f"✅ Extracted {len(extracted_text)} characters from image")
                    return extracted_text
                else:
                    logger.warning("⚠️ No text extracted from image")
                    return None
            else:
                logger.error(f"❌ OCR API request failed: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error extracting text from image: {e}")
            return None
    
    def process_pdf_with_local_ocr(self, pdf_path: str, focus_on_references: bool = True) -> Optional[str]:
        """
        Process PDF using local OCR with qwen2.5vl:3b.
        
        Args:
            pdf_path: Path to PDF file
            focus_on_references: Whether to focus on extracting references
            
        Returns:
            Extracted text from all pages or None if failed
        """
        if not self.available:
            logger.error("❌ Local OCR not available")
            return None
        
        logger.info(f"🔄 Processing PDF with local OCR: {pdf_path}")
        
        # Convert PDF to images
        image_paths = self.pdf_to_images(pdf_path)
        if not image_paths:
            logger.error("❌ Failed to convert PDF to images")
            return None
        
        all_text = []
        
        try:
            for i, image_path in enumerate(image_paths, 1):
                logger.info(f"🔄 Processing page {i}/{len(image_paths)}")
                
                text = self.extract_text_from_image(image_path, focus_on_references)
                if text:
                    all_text.append(f"--- Page {i} ---\n{text}\n")
                else:
                    logger.warning(f"⚠️ No text extracted from page {i}")
                
                # Clean up temporary image
                try:
                    os.unlink(image_path)
                except:
                    pass
            
            if all_text:
                combined_text = '\n'.join(all_text)
                logger.info(f"✅ Local OCR completed: {len(combined_text)} characters extracted")
                return combined_text
            else:
                logger.error("❌ No text extracted from any page")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error processing PDF with local OCR: {e}")
            return None
        
        finally:
            # Clean up any remaining temporary images
            for image_path in image_paths:
                try:
                    os.unlink(image_path)
                except:
                    pass
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the OCR model.
        
        Returns:
            Dictionary with model information
        """
        return {
            'model_name': self.model_name,
            'available': self.available,
            'ollama_url': self.ollama_base_url,
            'capabilities': [
                'Text extraction from images',
                'Reference detection and extraction',
                'Multi-page PDF processing',
                'Local processing (no external API calls)'
            ],
            'requirements': [
                'Ollama running locally',
                'qwen2.5vl:3b model installed',
                'pdf2image or PyMuPDF for PDF conversion'
            ]
        }
    
    def install_model_if_needed(self) -> bool:
        """
        Attempt to install the qwen2.5vl:3b model if not available.
        
        Returns:
            True if model is available after installation attempt
        """
        if self.available:
            return True
        
        try:
            logger.info(f"🔄 Attempting to install {self.model_name}")
            
            payload = {"name": self.model_name}
            response = requests.post(
                f"{self.ollama_base_url}/api/pull",
                json=payload,
                timeout=300  # 5 minutes timeout for model download
            )
            
            if response.status_code == 200:
                logger.info(f"✅ Successfully installed {self.model_name}")
                self.available = self._check_availability()
                return self.available
            else:
                logger.error(f"❌ Failed to install {self.model_name}: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error installing model: {e}")
            return False
