#!/usr/bin/env python3
"""
Find OneNote files in OneDrive folder.
"""

import os
from pathlib import Path

def find_onenote_files():
    """Find OneNote files in the user's OneDrive folder."""
    
    # Common OneDrive locations
    possible_paths = [
        Path.home() / "OneDrive",
        Path.home() / "OneDrive - Personal",
        Path.home() / "OneDrive - Business",
        Path("C:/Users") / os.getenv('USERNAME', '') / "OneDrive",
    ]
    
    print("🔍 Searching for OneNote files...")
    
    for onedrive_path in possible_paths:
        print(f"📁 Checking: {onedrive_path}")
        
        if onedrive_path.exists():
            print(f"✅ Found OneDrive folder: {onedrive_path}")
            
            # Look for OneNote files
            onenote_files = []
            
            # Search for .one files (OneNote section files)
            for one_file in onedrive_path.rglob("*.one"):
                onenote_files.append(('section', one_file))
            
            # Search for OneNote notebook folders
            for item in onedrive_path.rglob("*"):
                if item.is_dir():
                    # Check if it's a OneNote notebook folder
                    if any(child.suffix == '.one' for child in item.iterdir() if child.is_file()):
                        onenote_files.append(('notebook', item))
            
            print(f"📊 Found {len(onenote_files)} OneNote items:")
            
            for item_type, path in onenote_files:
                rel_path = path.relative_to(onedrive_path)
                print(f"   📄 {item_type}: {rel_path}")
                
                # Special attention to brain-related files
                if 'brain' in str(path).lower():
                    print(f"   🧠 BRAIN FILE FOUND: {path}")
                    
                    # Check file size
                    if path.is_file():
                        size = path.stat().st_size
                        print(f"      📊 Size: {size:,} bytes")
                    elif path.is_dir():
                        # Count files in directory
                        files_count = len(list(path.iterdir()))
                        print(f"      📊 Contains: {files_count} files")
            
            return onenote_files
        else:
            print(f"❌ Not found: {onedrive_path}")
    
    print("❌ No OneDrive folder found")
    return []

def analyze_brain_file():
    """Analyze the brain.one file specifically."""
    onenote_files = find_onenote_files()
    
    brain_files = [path for item_type, path in onenote_files if 'brain' in str(path).lower()]
    
    if brain_files:
        brain_file = brain_files[0]
        print(f"\n🧠 Analyzing Brain file: {brain_file}")
        
        if brain_file.exists():
            size = brain_file.stat().st_size
            print(f"📊 File size: {size:,} bytes")
            print(f"📅 Modified: {brain_file.stat().st_mtime}")
            print(f"📁 Full path: {brain_file.absolute()}")
            
            return brain_file
        else:
            print("❌ Brain file not accessible")
            return None
    else:
        print("❌ No brain.one file found")
        return None

if __name__ == "__main__":
    print("🌟" * 50)
    print("🔍 OneNote File Discovery")
    print("🌟" * 50)
    
    brain_file = analyze_brain_file()
    
    if brain_file:
        print(f"\n✅ SUCCESS! Found brain file at:")
        print(f"📁 {brain_file}")
        print(f"\n📋 Ready to process this file with automated pipeline!")
    else:
        print(f"\n❌ Could not find brain.one file")
        print(f"📋 Please check if OneNote is synced to OneDrive")
    
    print("\n🌟" * 50)
    print("🎉 Discovery Complete!")
    print("🌟" * 50)
