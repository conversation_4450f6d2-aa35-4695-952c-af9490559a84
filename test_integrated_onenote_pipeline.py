#!/usr/bin/env python3
"""
Test Integrated OneNote Pipeline

This script tests the newly integrated OneNote processing through the main
document processing pipeline to verify it generates similar results to PDF processing.
"""

import asyncio
import logging
import sys
import os
from pathlib import Path
from datetime import datetime

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.document_processing_service import get_document_processing_service
from utils.logging_utils import get_logger

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = get_logger(__name__)

class IntegratedOneNoteTest:
    """Test OneNote processing through the integrated pipeline."""
    
    def __init__(self):
        """Initialize the test."""
        self.results = {}
        
    async def test_brain_onenote_processing(self):
        """Test processing the brain.one file through the integrated pipeline."""
        
        # Find the most recent brain.one file
        uploads_dir = Path("uploads")
        brain_files = list(uploads_dir.glob("*Brain.one*"))
        
        if not brain_files:
            logger.error("No Brain.one files found in uploads directory")
            return False
            
        # Use the most recent one (by filename which includes date)
        brain_file = sorted(brain_files, key=lambda x: x.name)[-1]
        logger.info(f"🧠 Testing with file: {brain_file}")
        
        try:
            # Get the document processing service
            service = await get_document_processing_service()
            
            logger.info("🔄 Starting integrated OneNote processing...")
            start_time = datetime.now()
            
            # Process through the integrated pipeline
            result = await service.process_document(
                file_path=str(brain_file),
                chunk_size=1200,
                overlap=0,
                extract_entities=True,
                extract_references=True,
                extract_metadata=True,
                generate_embeddings=True
            )
            
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            if result.get("success", False):
                logger.info("✅ OneNote processing completed successfully!")
                
                # Extract key metrics
                self.results = {
                    "file_path": str(brain_file),
                    "file_type": result.get("file_type", "unknown"),
                    "episode_id": result.get("episode_id"),
                    "chunks": result.get("chunks", 0),
                    "facts": result.get("chunks", 0),  # chunks = facts in this context
                    "entities": result.get("entities_extracted", 0),
                    "references": result.get("references_extracted", 0),
                    "embeddings": result.get("embeddings_generated", 0),
                    "text_length": result.get("text_length", 0),
                    "processing_time": processing_time,
                    "duplicates_found": result.get("duplicates_found", 0),
                    "entities_merged": result.get("entities_merged", 0),
                    "metadata_extracted": result.get("metadata_extracted", False)
                }
                
                # Display results
                self.display_results()
                return True
                
            else:
                logger.error(f"❌ OneNote processing failed: {result.get('error', 'Unknown error')}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error during OneNote processing: {e}")
            return False
    
    def display_results(self):
        """Display the processing results in a formatted way."""
        
        print("\n" + "="*80)
        print("🧠 INTEGRATED ONENOTE PROCESSING RESULTS")
        print("="*80)
        
        print(f"📁 File: {Path(self.results['file_path']).name}")
        print(f"📄 File Type: {self.results['file_type']}")
        print(f"🆔 Episode ID: {self.results['episode_id']}")
        print(f"⏱️  Processing Time: {self.results['processing_time']:.2f} seconds")
        print(f"📝 Text Length: {self.results['text_length']:,} characters")
        
        print("\n📊 PROCESSING METRICS:")
        print(f"   📄 Facts (Chunks): {self.results['facts']}")
        print(f"   🏷️  Entities: {self.results['entities']}")
        print(f"   📚 References: {self.results['references']}")
        print(f"   🔗 Embeddings: {self.results['embeddings']}")
        
        print("\n🔄 DEDUPLICATION:")
        print(f"   🔍 Duplicates Found: {self.results['duplicates_found']}")
        print(f"   🔗 Entities Merged: {self.results['entities_merged']}")
        
        print(f"\n📋 Metadata Extracted: {'✅' if self.results['metadata_extracted'] else '❌'}")
        
        # Compare with typical PDF results
        print("\n📈 COMPARISON WITH TYPICAL PDF RESULTS:")
        print("   Expected ranges for a single document:")
        print("   📄 Facts: 50-100")
        print("   🏷️  Entities: 500-1000")
        print("   📚 References: 50-150")
        print("   🔗 Embeddings: 50-100")
        
        # Assess results
        facts_ok = 50 <= self.results['facts'] <= 200
        entities_ok = 200 <= self.results['entities'] <= 2000
        references_ok = 10 <= self.results['references'] <= 300
        embeddings_ok = 50 <= self.results['embeddings'] <= 200
        
        print(f"\n🎯 ASSESSMENT:")
        print(f"   📄 Facts: {'✅' if facts_ok else '⚠️'} ({self.results['facts']})")
        print(f"   🏷️  Entities: {'✅' if entities_ok else '⚠️'} ({self.results['entities']})")
        print(f"   📚 References: {'✅' if references_ok else '⚠️'} ({self.results['references']})")
        print(f"   🔗 Embeddings: {'✅' if embeddings_ok else '⚠️'} ({self.results['embeddings']})")
        
        overall_success = facts_ok and entities_ok and references_ok and embeddings_ok
        print(f"\n🎉 OVERALL: {'✅ SUCCESS' if overall_success else '⚠️ NEEDS REVIEW'}")
        
        print("="*80)

async def main():
    """Main test function."""
    
    print("🌟" * 60)
    print("🧠 TESTING INTEGRATED ONENOTE PROCESSING PIPELINE")
    print("🌟" * 60)
    
    tester = IntegratedOneNoteTest()
    
    success = await tester.test_brain_onenote_processing()
    
    if success:
        print("\n🎉 Test completed successfully!")
        print("\n📋 Next steps:")
        print("   1. Review the processing metrics above")
        print("   2. Check the knowledge graph for new entities")
        print("   3. Test search functionality with OneNote content")
        print("   4. Verify references were extracted properly")
    else:
        print("\n❌ Test failed - check errors above")
    
    print("\n🌟" * 60)
    print("🎉 Test Complete!")
    print("🌟" * 60)

if __name__ == "__main__":
    asyncio.run(main())
